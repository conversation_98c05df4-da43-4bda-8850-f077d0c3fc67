import { Response } from "express";
import { TransactionService } from "../services/transactionService";
import { OptimizedPDFService } from "../services/optimizedPdfService";
import { OptimizedZipService } from "../services/optimizedZipService";
import { ResponseHandler } from "../utils/response";
import { AuthenticatedRequest } from "../types";
import { asyncHandler } from "../middleware/errorHandler";
import { CacheInvalidationMiddleware } from "../middleware/cacheInvalidation";

/**
 * Optimized Transaction Controller with caching and performance improvements
 */
export class OptimizedTransactionController {
  
  /**
   * Optimized PDF download with caching and compression
   */
  static downloadTransactionPDF = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log(`📄 PDF download request for transaction ${transactionId} by user ${userId}`);

      try {
        // Get transaction data (this may be cached)
        const transaction = await TransactionService.getTransactionPreview(
          transactionId,
          userId,
          userRole
        );

        if (!transaction) {
          return ResponseHandler.error(
            res,
            "Transaction not found or access denied",
            undefined,
            404
          );
        }

        // Generate PDF (with caching)
        const pdfBuffer = await OptimizedPDFService.generateTransactionPDF(transaction);

        // Set optimized response headers
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="transaction-${transaction.transactionId}.pdf"`
        );
        res.setHeader("Content-Length", pdfBuffer.length);
        
        // Add caching headers for client-side caching
        res.setHeader("Cache-Control", "private, max-age=3600"); // 1 hour
        res.setHeader("ETag", `"pdf-${transaction.id}-${transaction.updatedAt}"`);
        
        // Add performance headers
        res.setHeader("X-Generated-By", "OptimizedPDFService");
        res.setHeader("X-Cache-Status", "optimized");

        console.log(`✅ PDF sent for transaction ${transactionId} (${pdfBuffer.length} bytes)`);
        res.send(pdfBuffer);
      } catch (error) {
        console.error(`❌ PDF generation failed for transaction ${transactionId}:`, error);
        return ResponseHandler.error(
          res,
          "Failed to generate PDF",
          error instanceof Error ? error.message : "Unknown error",
          500
        );
      }
    }
  );

  /**
   * Optimized ZIP download with parallel processing and caching
   */
  static downloadAll = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log(`📦 ZIP download request for transaction ${transactionId} by user ${userId}`);

      try {
        // Validate transaction and check access (cached)
        const validation = await OptimizedZipService.validateTransactionForZip(
          transactionId,
          userId,
          userRole
        );

        if (!validation.canAccess) {
          return ResponseHandler.error(
            res,
            "Transaction not found or access denied",
            undefined,
            404
          );
        }

        if (!validation.hasContent) {
          return ResponseHandler.error(
            res,
            "No content available for download",
            undefined,
            404
          );
        }

        // Get estimated file size for logging and headers
        const estimatedSize = await OptimizedZipService.getEstimatedZipSize(
          transactionId,
          userId,
          userRole
        );

        console.log(
          `📦 Creating optimized ZIP for transaction ${transactionId}, estimated size: ${estimatedSize} bytes`
        );

        // Create ZIP archive (with caching and parallel processing)
        const { zipBuffer, fileName } = await OptimizedZipService.createTransactionZip(
          transactionId,
          userId,
          userRole
        );

        // Set optimized response headers
        res.setHeader("Content-Type", "application/zip");
        res.setHeader(
          "Content-Disposition",
          `attachment; filename="${fileName}"`
        );
        res.setHeader("Content-Length", zipBuffer.length);
        
        // Add performance headers
        res.setHeader("X-Generated-By", "OptimizedZipService");
        res.setHeader("X-Estimated-Size", estimatedSize.toString());
        res.setHeader("X-Actual-Size", zipBuffer.length.toString());
        res.setHeader("X-Compression-Ratio", 
          ((estimatedSize - zipBuffer.length) / estimatedSize * 100).toFixed(2) + "%"
        );

        // Add caching headers
        res.setHeader("Cache-Control", "private, max-age=1800"); // 30 minutes
        res.setHeader("ETag", `"zip-${transactionId}-${Date.now()}"`);

        console.log(
          `✅ ZIP sent for transaction ${transactionId} (${zipBuffer.length} bytes, ${fileName})`
        );

        res.send(zipBuffer);
      } catch (error) {
        console.error(`❌ ZIP creation failed for transaction ${transactionId}:`, error);
        return ResponseHandler.error(
          res,
          "Failed to create ZIP archive",
          error instanceof Error ? error.message : "Unknown error",
          500
        );
      }
    }
  );

  /**
   * Get PDF generation statistics (for monitoring)
   */
  static getPDFStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const stats = OptimizedPDFService.getPDFStats();
      
      ResponseHandler.success(
        res,
        "PDF statistics retrieved successfully",
        stats
      );
    }
  );

  /**
   * Get ZIP service statistics (for monitoring)
   */
  static getZipStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const stats = OptimizedZipService.getZipStats();
      
      ResponseHandler.success(
        res,
        "ZIP statistics retrieved successfully",
        stats
      );
    }
  );

  /**
   * Clear PDF cache for a specific transaction (admin only)
   */
  static clearPDFCache = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      
      // Clear logo cache and general cache
      OptimizedPDFService.clearLogoCache();
      
      ResponseHandler.success(
        res,
        `PDF cache cleared for transaction ${transactionId}`
      );
    }
  );

  /**
   * Clear ZIP cache for a specific transaction (admin only)
   */
  static clearZipCache = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      
      OptimizedZipService.clearTransactionZipCache(transactionId);
      
      ResponseHandler.success(
        res,
        `ZIP cache cleared for transaction ${transactionId}`
      );
    }
  );

  /**
   * Warm up caches for a transaction (pre-generate PDF and ZIP)
   */
  static warmupTransaction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { transactionId } = req.params;
      const userId = req.user!.id;
      const userRole = req.user!.role;

      console.log(`🔥 Warming up caches for transaction ${transactionId}`);

      try {
        // Pre-generate PDF and ZIP in parallel
        const [transaction] = await Promise.all([
          TransactionService.getTransactionPreview(transactionId, userId, userRole),
        ]);

        if (!transaction) {
          return ResponseHandler.error(
            res,
            "Transaction not found or access denied",
            undefined,
            404
          );
        }

        // Warm up PDF and ZIP caches in parallel (don't wait for completion)
        Promise.all([
          OptimizedPDFService.generateTransactionPDF(transaction).catch(err => 
            console.warn("PDF warmup failed:", err)
          ),
          OptimizedZipService.createTransactionZip(transactionId, userId, userRole).catch(err => 
            console.warn("ZIP warmup failed:", err)
          ),
        ]);

        ResponseHandler.success(
          res,
          `Cache warmup initiated for transaction ${transactionId}`
        );
      } catch (error) {
        console.error(`❌ Cache warmup failed for transaction ${transactionId}:`, error);
        return ResponseHandler.error(
          res,
          "Failed to warm up caches",
          error instanceof Error ? error.message : "Unknown error",
          500
        );
      }
    }
  );
}
