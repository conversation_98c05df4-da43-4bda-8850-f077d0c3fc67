import { Router } from "express";
import { OptimizedTransactionController } from "../controllers/optimizedTransactionController";
import { authenticate, authorize } from "../middleware/auth";
import { UserRole } from "@prisma/client";
import { CacheInvalidationMiddleware } from "../middleware/cacheInvalidation";
import { PerformanceMonitoringService } from "../services/performanceMonitoringService";

const router = Router();

/**
 * Optimized transaction routes with caching and performance monitoring
 */

// Apply authentication to all routes
router.use(authenticate);

// Add performance monitoring to all routes
router.use(PerformanceMonitoringService.trackRequestPerformance());

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/pdf-optimized:
 *   get:
 *     summary: Download optimized transaction PDF
 *     description: Generate and download transaction PDF with caching and optimization
 *     tags: [Transactions - Optimized]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: PDF file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           X-Generated-By:
 *             description: Service that generated the PDF
 *             schema:
 *               type: string
 *           X-Cache-Status:
 *             description: Cache status
 *             schema:
 *               type: string
 *       404:
 *         description: Transaction not found
 *       500:
 *         description: PDF generation failed
 */
router.get(
  "/:transactionId/pdf-optimized",
  authorize(
    UserRole.SUPER_ADMIN,
    UserRole.ACCOUNT_OFFICER,
    UserRole.SUPERVISOR,
    UserRole.HEAD_CONSUMER_LENDING,
    UserRole.HEAD_RISK_MANAGEMENT,
    UserRole.MANAGING_DIRECTOR,
    UserRole.ACCOUNTANT
  ),
  OptimizedTransactionController.downloadTransactionPDF
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/download-all-optimized:
 *   get:
 *     summary: Download optimized transaction ZIP
 *     description: Create and download ZIP archive with all transaction documents using parallel processing and caching
 *     tags: [Transactions - Optimized]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: ZIP file
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           X-Generated-By:
 *             description: Service that generated the ZIP
 *             schema:
 *               type: string
 *           X-Estimated-Size:
 *             description: Estimated file size before compression
 *             schema:
 *               type: string
 *           X-Actual-Size:
 *             description: Actual compressed file size
 *             schema:
 *               type: string
 *           X-Compression-Ratio:
 *             description: Compression ratio achieved
 *             schema:
 *               type: string
 *       404:
 *         description: Transaction not found or no content available
 *       500:
 *         description: ZIP creation failed
 */
router.get(
  "/:transactionId/download-all-optimized",
  authorize(
    UserRole.SUPER_ADMIN,
    UserRole.ACCOUNT_OFFICER,
    UserRole.SUPERVISOR,
    UserRole.HEAD_CONSUMER_LENDING,
    UserRole.HEAD_RISK_MANAGEMENT,
    UserRole.MANAGING_DIRECTOR,
    UserRole.ACCOUNTANT
  ),
  OptimizedTransactionController.downloadAll
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/warmup:
 *   post:
 *     summary: Warm up transaction caches
 *     description: Pre-generate PDF and ZIP for faster subsequent downloads
 *     tags: [Transactions - Optimized]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: Cache warmup initiated
 *       404:
 *         description: Transaction not found
 *       500:
 *         description: Cache warmup failed
 */
router.post(
  "/:transactionId/warmup",
  authorize(
    UserRole.SUPER_ADMIN,
    UserRole.ACCOUNT_OFFICER,
    UserRole.SUPERVISOR,
    UserRole.HEAD_CONSUMER_LENDING,
    UserRole.HEAD_RISK_MANAGEMENT,
    UserRole.MANAGING_DIRECTOR,
    UserRole.ACCOUNTANT
  ),
  OptimizedTransactionController.warmupTransaction
);

// Admin-only routes for cache management and statistics

/**
 * @swagger
 * /api/v1/transactions/stats/pdf:
 *   get:
 *     summary: Get PDF generation statistics
 *     description: Retrieve PDF service performance statistics
 *     tags: [Transactions - Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: PDF statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 logosCached:
 *                   type: boolean
 *                 cacheHitRate:
 *                   type: number
 */
router.get(
  "/stats/pdf",
  authorize(UserRole.SUPER_ADMIN),
  OptimizedTransactionController.getPDFStats
);

/**
 * @swagger
 * /api/v1/transactions/stats/zip:
 *   get:
 *     summary: Get ZIP generation statistics
 *     description: Retrieve ZIP service performance statistics
 *     tags: [Transactions - Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: ZIP statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 cacheHitRate:
 *                   type: number
 *                 averageZipSize:
 *                   type: number
 *                 totalZipsCreated:
 *                   type: number
 */
router.get(
  "/stats/zip",
  authorize(UserRole.SUPER_ADMIN),
  OptimizedTransactionController.getZipStats
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/cache/pdf:
 *   delete:
 *     summary: Clear PDF cache
 *     description: Clear PDF cache for a specific transaction
 *     tags: [Transactions - Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: PDF cache cleared
 */
router.delete(
  "/:transactionId/cache/pdf",
  authorize(UserRole.SUPER_ADMIN),
  OptimizedTransactionController.clearPDFCache
);

/**
 * @swagger
 * /api/v1/transactions/{transactionId}/cache/zip:
 *   delete:
 *     summary: Clear ZIP cache
 *     description: Clear ZIP cache for a specific transaction
 *     tags: [Transactions - Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: transactionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Transaction ID
 *     responses:
 *       200:
 *         description: ZIP cache cleared
 */
router.delete(
  "/:transactionId/cache/zip",
  authorize(UserRole.SUPER_ADMIN),
  OptimizedTransactionController.clearZipCache
);

export default router;
